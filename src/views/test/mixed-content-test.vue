<template>
  <div class="mixed-content-test">
    <div class="header">
      <h2>混合内容处理测试</h2>
      <p>测试单元格中公式和文本的分离与处理功能</p>
    </div>

    <el-card class="test-section">
      <div slot="header">
        <span>单个内容测试</span>
      </div>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="测试内容:">
          <el-input
            v-model="testForm.content"
            type="textarea"
            :rows="3"
            placeholder="请输入包含公式的混合内容，如：温度: $T = 25 \pm 2$ ℃"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testSingleContent">测试分离</el-button>
          <el-button @click="clearResults">清空结果</el-button>
        </el-form-item>
      </el-form>

      <div v-if="separationResult" class="result-section">
        <h4>分离结果:</h4>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="原始内容">{{ separationResult.originalContent }}</el-descriptions-item>
          <el-descriptions-item label="是否包含公式">{{ separationResult.hasFormula ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="处理后内容">{{ separationResult.processedContent }}</el-descriptions-item>
          <el-descriptions-item label="公式数量">{{ separationResult.formulas.length }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="separationResult.formulas.length > 0" class="formulas-section">
          <h5>公式详情:</h5>
          <el-table :data="separationResult.formulas" border size="small">
            <el-table-column prop="index" label="序号" width="80"/>
            <el-table-column prop="type" label="类型" width="100"/>
            <el-table-column prop="original" label="原始公式" width="200"/>
            <el-table-column prop="content" label="公式内容" width="200"/>
            <el-table-column prop="placeholder" label="占位符" width="200"/>
            <el-table-column label="MathML" min-width="300">
              <template slot-scope="scope">
                <div v-if="mathMLResults[scope.row.placeholder]" class="mathml-result">
                  <el-tag size="mini" type="success">转换成功</el-tag>
                  <el-button size="mini" type="text" @click="showMathML(scope.row.placeholder)">查看</el-button>
                </div>
                <el-tag v-else size="mini" type="danger">转换失败</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <el-card class="test-section">
      <div slot="header">
        <span>批量测试</span>
      </div>
      
      <el-button type="primary" @click="runBatchTests">运行批量测试</el-button>
      <el-button type="success" @click="testWordExport">测试Word导出</el-button>
      
      <div v-if="batchTestResults.length > 0" class="batch-results">
        <h4>批量测试结果:</h4>
        <el-table :data="batchTestResults" border size="small">
          <el-table-column prop="content" label="测试内容" width="300"/>
          <el-table-column prop="hasFormula" label="包含公式" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.hasFormula ? 'success' : 'info'" size="mini">
                {{ scope.row.hasFormula ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="formulaCount" label="公式数量" width="100"/>
          <el-table-column prop="processedContent" label="处理后内容" min-width="300"/>
        </el-table>
      </div>
    </el-card>

    <!-- MathML查看对话框 -->
    <el-dialog title="MathML内容" :visible.sync="mathMLDialogVisible" width="60%">
      <el-input
        v-model="currentMathML"
        type="textarea"
        :rows="10"
        readonly
      />
      <div slot="footer">
        <el-button @click="mathMLDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyMathML">复制</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MixedContentTest',
  data() {
    return {
      testForm: {
        content: '温度: $T = 25 \\pm 2$ ℃，湿度: $H < 60\\%$'
      },
      separationResult: null,
      mathMLResults: {},
      batchTestResults: [],
      mathMLDialogVisible: false,
      currentMathML: '',
      mathUtils: null
    }
  },
  async mounted() {
    // 动态导入数学公式工具
    try {
      const mathFormulaUtils = await import('@/utils/math-formula-utils.js')
      this.mathUtils = mathFormulaUtils.default
      console.log('数学公式工具加载成功')
    } catch (error) {
      console.error('数学公式工具加载失败:', error)
      this.$message.error('数学公式工具加载失败')
    }
  },
  methods: {
    async testSingleContent() {
      if (!this.mathUtils) {
        this.$message.error('数学公式工具未加载')
        return
      }

      if (!this.testForm.content.trim()) {
        this.$message.warning('请输入测试内容')
        return
      }

      try {
        this.$message.info('开始分离测试...')
        
        // 分离公式和文本
        this.separationResult = this.mathUtils.separateFormulaAndText(this.testForm.content)
        
        // 转换公式为MathML
        this.mathMLResults = {}
        
        if (this.separationResult.hasFormula) {
          for (const formula of this.separationResult.formulas) {
            try {
              const mathML = await this.mathUtils.latexToMathML(formula.content)
              if (mathML) {
                this.mathMLResults[formula.placeholder] = mathML
              }
            } catch (error) {
              console.error(`公式转换失败: ${formula.content}`, error)
            }
          }
        }
        
        this.$message.success('分离测试完成')
        
      } catch (error) {
        console.error('测试失败:', error)
        this.$message.error('测试失败: ' + error.message)
      }
    },

    clearResults() {
      this.separationResult = null
      this.mathMLResults = {}
      this.batchTestResults = []
    },

    async runBatchTests() {
      if (!this.mathUtils) {
        this.$message.error('数学公式工具未加载')
        return
      }

      const testCases = [
        '温度: $T = 25 \\pm 2$ ℃',
        '长度: $L = 100$ mm, 宽度: $W = 50$ mm',
        '公式: $x^2 + y^2 = z^2$ 和 $\\frac{a}{b} = \\frac{c}{d}$',
        '纯文本内容，无公式',
        '$\\alpha + \\beta = \\gamma$',
        '前缀 $\\sqrt{x + y}$ 中间 $\\int_0^1 f(x)dx$ 后缀'
      ]

      this.batchTestResults = []
      
      for (const testCase of testCases) {
        const result = this.mathUtils.separateFormulaAndText(testCase)
        this.batchTestResults.push({
          content: testCase,
          hasFormula: result.hasFormula,
          formulaCount: result.formulas.length,
          processedContent: result.processedContent
        })
      }
      
      this.$message.success('批量测试完成')
    },

    showMathML(placeholder) {
      this.currentMathML = this.mathMLResults[placeholder] || ''
      this.mathMLDialogVisible = true
    },

    copyMathML() {
      navigator.clipboard.writeText(this.currentMathML).then(() => {
        this.$message.success('MathML已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    async testWordExport() {
      try {
        // 创建测试数据
        const testData = {
          title: '混合内容测试表格',
          tableWidth: 1200,
          tableHeight: 600,
          pageOrientation: 'LANDSCAPE',
          tableData: {
            headers: [[
              { content: '项目', rowspan: 1, colspan: 1, width: 200, height: 50 },
              { content: '要求', rowspan: 1, colspan: 1, width: 300, height: 50 },
              { content: '结果', rowspan: 1, colspan: 1, width: 300, height: 50 }
            ]],
            dataRows: []
          }
        }

        // 处理测试数据
        const processedData = await this.mathUtils.processTableData(testData.tableData)
        testData.tableData = processedData

        console.log('测试数据:', testData)
        this.$message.success('Word导出测试数据已准备，请查看控制台')
        
      } catch (error) {
        console.error('Word导出测试失败:', error)
        this.$message.error('Word导出测试失败')
      }
    }
  }
}
</script>

<style scoped>
.mixed-content-test {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.test-section {
  margin-bottom: 20px;
}

.result-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.formulas-section {
  margin-top: 15px;
}

.batch-results {
  margin-top: 20px;
}

.mathml-result {
  display: flex;
  align-items: center;
  gap: 10px;
}

h4, h5 {
  color: #303133;
  margin: 10px 0;
}
</style>
